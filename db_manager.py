import sqlite3
import os
from typing import List

import pandas as pd

from article import Article

# 使用绝对路径，确保所有地方都使用同一个数据库文件
DB_PATH = os.path.join("/Users/<USER>/workspace/news", 'news.db')
TABLE_NAME = "news"


class DBManager:

    @staticmethod
    def drop():
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute(f'DROP TABLE {TABLE_NAME}')

    # 初始化数据库和表结构
    @staticmethod
    def init_database():
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS news (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                source TEXT NOT NULL,
                url TEXT NOT NULL,
                cat TEXT NOT NULL,
                published_at TEXT NOT NULL,
                extra TEXT
            )
        ''')

        conn.commit()
        conn.close()

    # 从数据库读取新闻
    @staticmethod
    def load_article_list() -> List[Article]:
        conn = sqlite3.connect(DB_PATH)
        query = '''
            SELECT title, content, source, url, cat, published_at, extra
            FROM news
            ORDER BY published_at DESC
        '''
        df = pd.read_sql_query(query, conn)
        conn.close()

        # 转换为Article对象列表
        article_list = []
        for _, row in df.iterrows():
            article = Article(
                title=row['title'],
                content=row['content'],
                source=row['source'],
                url=row['url'],
                cat=row['cat'],
                published_at=row['published_at'],
                extra=row['extra'] if row['extra'] is not None else ""
            )
            article_list.append(article)

        return article_list

    # 插入新闻数据到数据库
    @staticmethod
    def batch_insert_articles(insert_list: List[Article]):
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        for article in insert_list:
            cursor.execute('''
                INSERT INTO news (title, content, source, url, cat, published_at, extra)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                article.title,
                article.content,
                article.source,
                article.url,
                article.cat,
                article.published_at,
                article.extra
            ))
        conn.commit()
        conn.close()

    # 查询特定数据源的最新时间
    @staticmethod
    def get_latest_time_by_source(source_name: str) -> str:
        """
        查询指定数据源的最新发布时间

        Args:
            source_name: 数据源名称，如 "同花顺_7*24"

        Returns:
            最新时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"，如果没有数据则返回 None
        """
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT MAX(published_at)
            FROM news
            WHERE source = ?
        ''', (source_name,))

        result = cursor.fetchone()
        conn.close()

        return result[0] if result and result[0] else None

    # 批量插入新闻数据（带去重）
    @staticmethod
    def batch_insert_articles_with_dedup(insert_list: List[Article]) -> int:
        """
        批量插入新闻数据，自动去重

        Args:
            insert_list: 要插入的文章列表

        Returns:
            实际插入的数量
        """
        if not insert_list:
            return 0

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        inserted_count = 0

        for article in insert_list:
            # 检查URL是否已存在
            cursor.execute('SELECT 1 FROM news WHERE url = ?', (article.url,))
            if cursor.fetchone():
                continue  # 跳过重复的URL

            cursor.execute('''
                INSERT INTO news (title, content, source, url, cat, published_at, extra)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                article.title,
                article.content,
                article.source,
                article.url,
                article.cat,
                article.published_at,
                article.extra
            ))
            inserted_count += 1

        conn.commit()
        conn.close()
        return inserted_count

    # 获取所有唯一的新闻源
    @staticmethod
    def get_unique_sources() -> List[str]:
        """
        获取数据库中所有唯一的新闻源列表

        Returns:
            新闻源名称列表，按字母顺序排序
        """
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT DISTINCT source
            FROM news
            WHERE source IS NOT NULL AND source != ''
            ORDER BY source
        ''')

        results = cursor.fetchall()
        conn.close()

        return [result[0] for result in results]


if __name__ == '__main__':
    db = DBManager()
    # db.drop()
    # exit()
    db.init_database()
    article_list = db.load_article_list()
    for article in article_list:
        print(article)
