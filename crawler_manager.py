"""
爬虫管理器 - 支持多种数据源的增量更新
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Type, Optional

from crawler.Spider_THS_724 import Spider_THS_724
from crawler.Spider_Sina_724 import Spider_Sina_724
from crawler.Spider_THS_finance import Spider_THS_Finance
from crawler.Spider_East_Money_ShangPin import Spider_East_Money_ShangPin
from crawler.Spider_East_Money_724 import Spider_East_Money_724
from crawler.BaseSpider import BaseSpider
from db_manager import DBManager
from article import Article

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CrawlerManager:
    """爬虫管理器，支持多种数据源的增量更新"""

    def __init__(self):
        self.db_manager = DBManager()
        # 注册所有可用的爬虫类
        self.spider_registry: Dict[str, Type[BaseSpider]] = {
            "同花顺_7*24": Spider_THS_724,
            "新浪财经_7*24": Spider_Sina_724,
            "同花顺_财经_财经要闻": Spider_THS_Finance,
            "东方财富_全球财经快讯_商品": Spider_East_Money_ShangPin,
            "东方财富_全球财经快讯_7*24": Spider_East_Money_724,
        }

    def get_available_sources(self) -> List[str]:
        """获取所有可用的数据源列表"""
        return list(self.spider_registry.keys())

    def get_latest_time_for_source(self, source_name: str) -> Optional[str]:
        """获取指定数据源的最新时间"""
        return self.db_manager.get_latest_time_by_source(source_name)

    def calculate_crawl_duration(self, latest_time: str) -> int:
        """
        计算需要爬取的时间范围（分钟）
        
        Args:
            latest_time: 最新时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"
            
        Returns:
            需要爬取的分钟数
        """
        if not latest_time:
            # 如果没有历史数据，默认爬取最近24小时
            return 24 * 60

        try:
            latest_datetime = datetime.strptime(latest_time, "%Y-%m-%d %H:%M:%S")
            now = datetime.now()
            duration = now - latest_datetime
            # 转换为分钟，并添加一些缓冲时间
            minutes = int(duration.total_seconds() / 60) + 30
            return max(minutes, 60)  # 至少爬取1小时
        except ValueError as e:
            logger.error(f"时间格式解析错误: {latest_time}, {e}")
            return 24 * 60

    def crawl_incremental_data(self, source_name: str) -> Dict:
        """
        为指定数据源执行增量爬取

        Args:
            source_name: 数据源名称

        Returns:
            包含爬取结果的字典
        """
        result = {
            "success": False,
            "message": "",
            "crawled_count": 0,
            "inserted_count": 0,
            "latest_time": None,
            "duration_minutes": 0,
            "error_type": None,
            "error_details": None,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        try:
            # 检查数据源是否支持
            if source_name not in self.spider_registry:
                result["error_type"] = "UNSUPPORTED_SOURCE"
                result["message"] = f"不支持的数据源: {source_name}"
                result["error_details"] = f"可用数据源: {', '.join(self.spider_registry.keys())}"
                _crawl_status_cache[source_name] = result
                return result

            # 获取最新时间
            latest_time = self.get_latest_time_for_source(source_name)
            result["latest_time"] = latest_time

            # 计算爬取时间范围
            duration_minutes = self.calculate_crawl_duration(latest_time)
            result["duration_minutes"] = duration_minutes

            logger.info(f"开始增量爬取 {source_name}，最新时间: {latest_time}，爬取范围: {duration_minutes}分钟")

            # 创建爬虫实例并执行爬取
            spider_class = self.spider_registry[source_name]
            try:
                spider = spider_class(source_id=1, db_conn=None)  # 这里的source_id暂时用1
            except Exception as e:
                result["error_type"] = "SPIDER_INIT_ERROR"
                result["message"] = f"爬虫初始化失败: {str(e)}"
                result["error_details"] = f"爬虫类: {spider_class.__name__}"
                logger.error(f"爬虫初始化失败: {e}", exc_info=True)
                _crawl_status_cache[source_name] = result
                return result

            # 执行简单的爬取（只爬取第一页，避免复杂的时间逻辑）
            crawled_articles = []

            # 爬取多页数据直到获取足够的历史数据
            max_pages = 10  # 最多爬取10页
            end_time = datetime.now() - timedelta(minutes=duration_minutes)

            for page in range(1, max_pages + 1):
                try:
                    page_url = spider._get_page_url(page)

                    # 网络请求错误处理
                    try:
                        html = spider.fetch(page_url)
                        if not html or len(html.strip()) == 0:
                            logger.warning(f"第{page}页返回空内容")
                            result["error_type"] = "EMPTY_RESPONSE"
                            result["error_details"] = f"页面URL: {page_url}"
                            break
                    except Exception as fetch_error:
                        logger.error(f"网络请求失败 - 第{page}页: {fetch_error}")
                        result["error_type"] = "NETWORK_ERROR"
                        result["error_details"] = f"页面URL: {page_url}, 错误: {str(fetch_error)}"
                        if page == 1:  # 如果第一页就失败，直接返回错误
                            result["message"] = f"网络请求失败: {str(fetch_error)}"
                            _crawl_status_cache[source_name] = result
                            return result
                        break

                    # 解析错误处理
                    try:
                        page_articles = spider.parse(html)
                    except Exception as parse_error:
                        logger.error(f"页面解析失败 - 第{page}页: {parse_error}")
                        result["error_type"] = "PARSE_ERROR"
                        result["error_details"] = f"页面URL: {page_url}, 错误: {str(parse_error)}"
                        if page == 1:  # 如果第一页就失败，直接返回错误
                            result["message"] = f"页面解析失败: {str(parse_error)}"
                            _crawl_status_cache[source_name] = result
                            return result
                        break

                    if not page_articles:
                        logger.info(f"第{page}页没有获取到文章数据")
                        break

                    # 检查是否已经爬取到足够旧的数据
                    oldest_article = page_articles[-1]
                    try:
                        oldest_time = datetime.strptime(oldest_article.published_at, "%Y-%m-%d %H:%M:%S")
                    except ValueError as time_error:
                        logger.error(f"时间格式解析错误: {oldest_article.published_at}, {time_error}")
                        result["error_type"] = "TIME_FORMAT_ERROR"
                        result["error_details"] = f"时间字符串: {oldest_article.published_at}"
                        continue

                    # 过滤出新于最新时间的文章
                    if latest_time:
                        try:
                            latest_datetime = datetime.strptime(latest_time, "%Y-%m-%d %H:%M:%S")
                            new_articles = [a for a in page_articles if
                                            datetime.strptime(a.published_at, "%Y-%m-%d %H:%M:%S") > latest_datetime]
                            crawled_articles.extend(new_articles)
                        except ValueError as time_error:
                            logger.error(f"时间比较错误: {time_error}")
                            result["error_type"] = "TIME_COMPARISON_ERROR"
                            result["error_details"] = f"最新时间: {latest_time}"
                            crawled_articles.extend(page_articles)
                    else:
                        # 如果没有历史数据，添加所有文章
                        crawled_articles.extend(page_articles)

                    # 如果最老的文章时间已经早于目标时间，停止爬取
                    if oldest_time < end_time:
                        break

                except Exception as e:
                    logger.error(f"爬取第{page}页时出现未知错误: {e}", exc_info=True)
                    result["error_type"] = "UNKNOWN_PAGE_ERROR"
                    result["error_details"] = f"页面: {page}, 错误: {str(e)}"
                    if page == 1:  # 如果第一页就失败，直接返回错误
                        result["message"] = f"爬取失败: {str(e)}"
                        _crawl_status_cache[source_name] = result
                        return result
                    break

            result["crawled_count"] = len(crawled_articles)

            if not crawled_articles:
                result["message"] = "没有爬取到新数据"
                result["success"] = True
                if result.get("error_type"):
                    result["message"] += f" (可能原因: {result['error_type']})"
                _crawl_status_cache[source_name] = result
                return result

            # 将爬取结果插入数据库
            try:
                inserted_count = self.db_manager.batch_insert_articles_with_dedup(crawled_articles)
                result["inserted_count"] = inserted_count
            except Exception as db_error:
                logger.error(f"数据库插入失败: {db_error}", exc_info=True)
                result["error_type"] = "DATABASE_ERROR"
                result["message"] = f"数据库插入失败: {str(db_error)}"
                result["error_details"] = f"尝试插入 {len(crawled_articles)} 条数据"
                _crawl_status_cache[source_name] = result
                return result

            result["success"] = True
            result["message"] = f"成功爬取 {len(crawled_articles)} 条数据，新增 {inserted_count} 条"

            # 如果有部分错误但仍然成功，添加警告信息
            if result.get("error_type"):
                result["message"] += f" (警告: {result['error_type']})"

            logger.info(f"增量爬取完成: {result['message']}")

        except Exception as e:
            error_msg = f"爬取过程中发生未知错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            result["error_type"] = "UNKNOWN_ERROR"
            result["message"] = error_msg
            result["error_details"] = str(e)

        # 缓存结果状态（无论成功还是失败都要缓存）
        _crawl_status_cache[source_name] = result

        return result

    def crawl_all_sources(self) -> Dict[str, Dict]:
        """
        为所有数据源执行增量爬取
        
        Returns:
            每个数据源的爬取结果字典
        """
        results = {}

        for source_name in self.get_available_sources():
            logger.info(f"开始处理数据源: {source_name}")
            results[source_name] = self.crawl_incremental_data(source_name)

        return results

    def register_spider(self, source_name: str, spider_class: Type[BaseSpider]):
        """
        注册新的爬虫类
        
        Args:
            source_name: 数据源名称
            spider_class: 爬虫类
        """
        self.spider_registry[source_name] = spider_class
        logger.info(f"已注册新的爬虫: {source_name}")


# 全局实例
crawler_manager = CrawlerManager()

# 全局状态存储（用于在UI中显示状态）
_crawl_status_cache = {}


def get_crawl_status_summary() -> Dict:
    """获取爬取状态摘要"""
    if not _crawl_status_cache:
        return {
            "total_sources": len(crawler_manager.get_available_sources()),
            "successful_sources": 0,
            "failed_sources": 0,
            "last_update": "从未更新",
            "has_errors": False,
            "error_sources": []
        }

    successful = sum(1 for result in _crawl_status_cache.values() if result.get("success", False))
    failed = len(_crawl_status_cache) - successful
    error_sources = [source for source, result in _crawl_status_cache.items() if not result.get("success", False)]

    # 获取最新的更新时间
    latest_timestamp = max(
        (result.get("timestamp", "1970-01-01 00:00:00") for result in _crawl_status_cache.values()),
        default="从未更新"
    )

    return {
        "total_sources": len(crawler_manager.get_available_sources()),
        "successful_sources": successful,
        "failed_sources": failed,
        "last_update": latest_timestamp,
        "has_errors": failed > 0,
        "error_sources": error_sources
    }


def get_last_crawl_errors():
    """获取最近的爬取错误信息"""
    return {source: result for source, result in _crawl_status_cache.items()
            if not result.get("success", False)}


def show_crawl_status_ui():
    """显示爬取状态的UI组件"""
    import streamlit as st

    status = get_crawl_status_summary()

    # 状态指示器
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if status["has_errors"]:
            st.metric("🔴 数据源状态", f"{status['successful_sources']}/{status['total_sources']}",
                     delta=f"-{status['failed_sources']} 失败", delta_color="inverse")
        else:
            st.metric("🟢 数据源状态", f"{status['successful_sources']}/{status['total_sources']}",
                     delta="全部正常", delta_color="normal")

    with col2:
        st.metric("最后更新", status["last_update"].split()[1] if " " in status["last_update"] else status["last_update"])

    with col3:
        if status["has_errors"]:
            st.metric("⚠️ 错误数量", status["failed_sources"], delta_color="inverse")
        else:
            st.metric("✅ 运行状态", "正常")

    with col4:
        # 显示错误源（如果有）
        if status["error_sources"]:
            error_text = ", ".join(status["error_sources"][:2])  # 只显示前两个
            if len(status["error_sources"]) > 2:
                error_text += f" 等{len(status['error_sources'])}个"
            st.metric("❌ 失败源", error_text)

    # 如果有错误，显示详细信息
    if status["has_errors"] and _crawl_status_cache:
        with st.expander("🔍 查看错误详情", expanded=False):
            for source_name in status["error_sources"]:
                if source_name in _crawl_status_cache:
                    result = _crawl_status_cache[source_name]
                    st.error(f"**{source_name}**")
                    st.text(f"错误: {result.get('message', '未知错误')}")
                    if result.get("error_type"):
                        st.text(f"类型: {result['error_type']}")
                    if result.get("error_details"):
                        st.text(f"详情: {result['error_details']}")
                    st.text(f"时间: {result.get('timestamp', '未知')}")
                    st.divider()


def update_news_data_ui():
    """Streamlit UI组件：数据更新界面"""
    import streamlit as st

    st.subheader("📡 数据更新")

    # 显示爬取状态
    show_crawl_status_ui()
    st.divider()

    # 显示可用数据源
    available_sources = crawler_manager.get_available_sources()

    col1, col2 = st.columns([2, 1])

    with col1:
        st.write("**可用数据源:**")
        for source in available_sources:
            latest_time = crawler_manager.get_latest_time_for_source(source)
            if latest_time:
                st.write(f"• {source}: 最新数据时间 {latest_time}")
            else:
                st.write(f"• {source}: 暂无数据")

    with col2:
        # 单个数据源更新
        selected_source = st.selectbox("选择数据源", available_sources)

        if st.button("🔄 更新选中数据源", key="update_single"):
            with st.spinner(f"正在更新 {selected_source} 数据..."):
                result = crawler_manager.crawl_incremental_data(selected_source)

                if result["success"]:
                    st.success(result["message"])
                    if result["inserted_count"] > 0:
                        st.balloons()
                    # 显示警告信息（如果有）
                    if result.get("error_type") and result.get("error_details"):
                        with st.expander("⚠️ 警告详情", expanded=False):
                            st.warning(f"**错误类型:** {result['error_type']}")
                            st.text(f"详细信息: {result['error_details']}")
                            st.text(f"更新时间: {result['timestamp']}")
                else:
                    st.error(result["message"])
                    # 显示详细错误信息
                    if result.get("error_type") or result.get("error_details"):
                        with st.expander("🔍 错误详情", expanded=True):
                            if result.get("error_type"):
                                st.error(f"**错误类型:** {result['error_type']}")
                            if result.get("error_details"):
                                st.text(f"**详细信息:** {result['error_details']}")
                            st.text(f"**发生时间:** {result['timestamp']}")
                            st.text(f"**数据源:** {selected_source}")

                            # 提供重试建议
                            error_type = result.get("error_type", "")
                            if "NETWORK" in error_type:
                                st.info("💡 **建议:** 网络连接问题，请检查网络连接后重试")
                            elif "PARSE" in error_type:
                                st.info("💡 **建议:** 页面解析失败，可能是网站结构发生变化")
                            elif "DATABASE" in error_type:
                                st.info("💡 **建议:** 数据库操作失败，请检查数据库连接")
                            elif "SPIDER_INIT" in error_type:
                                st.info("💡 **建议:** 爬虫初始化失败，请检查爬虫配置")
                            else:
                                st.info("💡 **建议:** 请稍后重试，如问题持续存在请联系管理员")

        # 全部数据源更新
        if st.button("🚀 更新所有数据源", key="update_all"):
            with st.spinner("正在更新所有数据源..."):
                results = crawler_manager.crawl_all_sources()

                success_count = 0
                total_inserted = 0
                failed_sources = []

                for source_name, result in results.items():
                    if result["success"]:
                        success_count += 1
                        total_inserted += result["inserted_count"]
                        if result["inserted_count"] > 0:
                            st.success(f"✅ {source_name}: {result['message']}")
                        else:
                            st.info(f"ℹ️ {source_name}: {result['message']}")

                        # 显示警告信息（如果有）
                        if result.get("error_type") and result.get("error_details"):
                            with st.expander(f"⚠️ {source_name} 警告详情", expanded=False):
                                st.warning(f"错误类型: {result['error_type']}")
                                st.text(f"详细信息: {result['error_details']}")
                    else:
                        failed_sources.append((source_name, result))
                        st.error(f"❌ {source_name}: {result['message']}")

                # 显示总体结果
                if success_count > 0:
                    st.success(f"✅ 成功更新 {success_count}/{len(results)} 个数据源，共新增 {total_inserted} 条数据")
                    if total_inserted > 0:
                        st.balloons()
                else:
                    st.error("❌ 所有数据源更新失败")

                # 显示失败数据源的详细信息
                if failed_sources:
                    with st.expander(f"🔍 失败详情 ({len(failed_sources)} 个数据源)", expanded=len(failed_sources) == len(results)):
                        for source_name, result in failed_sources:
                            st.subheader(f"❌ {source_name}")
                            if result.get("error_type"):
                                st.error(f"错误类型: {result['error_type']}")
                            if result.get("error_details"):
                                st.text(f"详细信息: {result['error_details']}")
                            st.text(f"发生时间: {result['timestamp']}")
                            st.divider()
        col_refresh, col_clear = st.columns(2)
        with col_refresh:
            if st.button("🔄 刷新数据显示", key="refresh"):
                st.rerun()
        with col_clear:
            if st.button("🗑️ 清除错误记录", key="clear_errors"):
                _crawl_status_cache.clear()
                st.success("错误记录已清除")
                st.rerun()


if __name__ == "__main__":
    # 测试代码
    manager = CrawlerManager()
    print("可用数据源:", manager.get_available_sources())

    # 测试THS_724数据源
    result = manager.crawl_incremental_data("同花顺_7*24")
    print("爬取结果:", result)
