THS_724 = "同花顺_7*24"
SOURCE_LIST = [THS_724]

TIME_COL = "时间"
BDI_COL = "BDI"
EXCHANGE_COL = "现汇卖出价"
RATE_COL = "美联储利率"
FED_COL = "美联储加降息"
WORKDAY_COL = "工作日"
WEEKDAY_COL = "星期"
FED_RATE_INCREASE = "加息"
FED_RATE_DECREASE = "降息"
FED_RATE_STABLE = "稳定"
# 总手又名成交手（量）
VOL_COL = "总手"
CLOSING_PRICE_COL = "收盘"
HIGHEST_PRICE_COL = "最高"
LOWEST_PRICE_COL = "最低"
OPEN_PRICE_COL = "开盘"
# ToDo(hm): 应该去掉「万元」，但注意要和历史数据兼容
TURNOVER_COL = "成交额(万元)"
CONTRACT_CODE_COL = "合约代码"
FEATURE_COLS = [CLOSING_PRICE_COL, OPEN_PRICE_COL, HIGHEST_PRICE_COL, LOWEST_PRICE_COL, VOL_COL]
CLOSING_PRICE_RATIO_COL = "收盘涨幅"
HIGHEST_PRICE_RATIO_COL = "最高涨幅"
LOWEST_PRICE_RATIO_COL = "最低涨幅"
LABEL_COLS = [CLOSING_PRICE_RATIO_COL, HIGHEST_PRICE_RATIO_COL, LOWEST_PRICE_RATIO_COL]
OUTER_CRAWL_RETRY_NUM = 3
INNER_CRAWL_RETRY_NUM = 10
NA_VAL = "NA_VAL"
DATE_FORMAT = "%Y-%m-%d"
TIME_FORMAT = "%Y-%m-%d_%H-%M-%S"
READABLE_TIME_FORMAT = "%Y-%m-%d %H:%M:%S"
READABLE_TIME_FORMAT_SHORT = "%H:%M:%S"
SIGNAL_VAL_1 = "1"
SIGNAL_VAL_2 = "2"
SIGNAL_VAL_3 = "3"
SIGNAL_VAL_4 = "4"
SIGNAL_VAL_5 = "5"
SIGNAL_VAL_6 = "6"
SIGNAL_VAL_LIST = [
    SIGNAL_VAL_1,
    SIGNAL_VAL_2,
    SIGNAL_VAL_3,
    SIGNAL_VAL_4,
    SIGNAL_VAL_5,
    SIGNAL_VAL_6,
]
SIGNAL_TYPE_1 = "1"
SIGNAL_TYPE_2 = "2"
SIGNAL_TYPE_3 = "3"
SIGNAL_TYPE_4 = "4"
SIGNAL_TYPE_5 = "5"
SIGNAL_TYPE_6 = "6"
REQUEST_TIMEOUT = 10
TECH_SIGNAL_NAME_1 = "交易类指标_1"
TECH_SIGNAL_NAME_2 = "交易类指标_2"
TECH_SIGNAL_NAME_3 = "交易类指标_3"
TECH_SIGNAL_NAME_4 = "交易类指标_4"
TECH_SIGNAL_NAME_5 = "交易类指标_5"
TECH_SIGNAL_NAME_6 = "交易类指标_6"
TECH_SIGNAL_NAME_7 = "交易类指标_7"
TECH_SIGNAL_NAME_8 = "交易类指标_8"
TECH_SIGNAL_NAME_9 = "交易类指标_9"
TECH_SIGNAL_NAME_10 = "交易类指标_10"
TECH_SIGNAL_NAME_11 = "交易类指标_11"
TECH_SIGNAL_NAME_12 = "交易类指标_12"
TECH_SIGNAL_NAME_13 = "交易类指标_13"
INFO_SIGNAL_NAME_1 = "信息类指标_1"
INFO_SIGNAL_NAME_2 = "信息类指标_2"
INFO_SIGNAL_NAME_3 = "信息类指标_3"
INFO_SIGNAL_NAME_4 = "信息类指标_4"
MAN_SIGNAL_NAME_1 = "人工类指标_1"
MAN_SIGNAL_NAME_2 = "人工类指标_2"
MAN_SIGNAL_NAME_3 = "人工类指标_3"
MAN_SIGNAL_NAME_4 = "人工类指标_4"
MAN_SIGNAL_NAME_5 = "人工类指标_5"
MAN_SIGNAL_NAME_6 = "人工类指标_6"
MAN_SIGNAL_NAME_7 = "人工类指标_7"
MAN_SIGNAL_NAME_8 = "人工类指标_8"
CZCE_SR = "郑商所-白糖"
CZCE_FG = "郑商所-玻璃"
CZCE_SA = "郑商所-纯碱"
DCE_LH = "大商所-生猪"
DCE_C = "大商所-玉米"
DCE_P = "大商所-棕榈油"
DCE_I = "大商所-铁矿石"
SHFE_RU = "上期所-天然橡胶"
SHFE_WR = "上期所-螺纹钢"
COMMON_TECH_SIGNAL_LIST = [
    TECH_SIGNAL_NAME_1,
    TECH_SIGNAL_NAME_2,
    TECH_SIGNAL_NAME_3,
    TECH_SIGNAL_NAME_4,
    TECH_SIGNAL_NAME_5,
    TECH_SIGNAL_NAME_6,
    TECH_SIGNAL_NAME_7,
    TECH_SIGNAL_NAME_8,
    TECH_SIGNAL_NAME_9,
    TECH_SIGNAL_NAME_10,
    TECH_SIGNAL_NAME_12,
    TECH_SIGNAL_NAME_13,
]
TECH_SIGNAL_NAME_DICT = {
    CZCE_FG: COMMON_TECH_SIGNAL_LIST,
    CZCE_SA: COMMON_TECH_SIGNAL_LIST,
    CZCE_SR: COMMON_TECH_SIGNAL_LIST,
    SHFE_RU: COMMON_TECH_SIGNAL_LIST,
    SHFE_WR: COMMON_TECH_SIGNAL_LIST,
    DCE_C: [
        TECH_SIGNAL_NAME_1,
        TECH_SIGNAL_NAME_2,
        TECH_SIGNAL_NAME_3,
        TECH_SIGNAL_NAME_4,
        TECH_SIGNAL_NAME_5,
        TECH_SIGNAL_NAME_6,
        TECH_SIGNAL_NAME_7,
        TECH_SIGNAL_NAME_8,
        TECH_SIGNAL_NAME_9,
        TECH_SIGNAL_NAME_10,
        TECH_SIGNAL_NAME_11,
        TECH_SIGNAL_NAME_12,
        TECH_SIGNAL_NAME_13,
    ],
    DCE_P: COMMON_TECH_SIGNAL_LIST,
    DCE_I: COMMON_TECH_SIGNAL_LIST,
}
INFO_SIGNAL_NAME_LIST = [
    INFO_SIGNAL_NAME_1,
    INFO_SIGNAL_NAME_2,
    INFO_SIGNAL_NAME_3,
    INFO_SIGNAL_NAME_4,
]
MAN_SIGNAL_NAME_LIST = [
    MAN_SIGNAL_NAME_1,
    MAN_SIGNAL_NAME_2,
    MAN_SIGNAL_NAME_3,
    MAN_SIGNAL_NAME_4,
    MAN_SIGNAL_NAME_5,
    MAN_SIGNAL_NAME_6,
    MAN_SIGNAL_NAME_7,
    MAN_SIGNAL_NAME_8,
]
WEIGHT_OP_SEQ_SMALL_FIRST_OPTION = "先调绝对值小的"
WEIGHT_OP_SEQ_LARGE_FIRST_OPTION = "先调绝对值大的"
HUMAN_SIGNAL_IGNORE_OPTION = "不单独设置人工类指标权重优先级"
HUMAN_SIGNAL_FIRST_OPTION_LIST = [
    "后调人工类指标权重",
    "先调人工类指标权重",
    HUMAN_SIGNAL_IGNORE_OPTION,
]
WEIGHT_OP_SEQ_OPTION_LIST = [WEIGHT_OP_SEQ_SMALL_FIRST_OPTION, WEIGHT_OP_SEQ_LARGE_FIRST_OPTION]
TRAIN_LENGTH_KEY = "训练样本数量"
HUMAN_SIGNAL_FIRST_KEY = "是否先调人工类指标权重"
WEIGHT_OP_SEQ_KEY = "权重优化顺序"
USE_LAST_WEIGHT_KEY = "使用之前的权重"
SELECTED_SIGNAL_LIST_KEY = "使用的指标"

CHECKPOINT_PATH_KEY = "checkpoint路径"

REF_CKP_PATH = "参考择时"
DAILY_CHOOSE_TIME = "日常择时"
RUN_TYPE = "执行类型"
RUN_SOURCE = "执行来源"
CAT = "品种"
PID = "pid"
RUN_PARAM = "运行参数"
USE_EXPERIMENT_REF_WEIGHT_KEY = "使用之前实验结果初始化罕见信号权重"
RARE_SIGNAL_NUM_THRESHOLD_KEY = "当信号出现次数少于多少次时使用历史权重"
RARE_SIGNAL_NUM_THRESHOLD_DEFAULT = 3
INIT_WEIGHT = 0.01
CZCE_CAT_LIST = [CZCE_SR, CZCE_FG, CZCE_SA]
SHFE_CAT_LIST = [SHFE_RU, SHFE_WR]
DCE_CAT_LIST = [DCE_C, DCE_LH, DCE_P, DCE_I]
CHOOSE_CAT_LIST = CZCE_CAT_LIST + SHFE_CAT_LIST + [DCE_C, DCE_P, DCE_I]
TECH_CAT_LIST = CZCE_CAT_LIST + SHFE_CAT_LIST + DCE_CAT_LIST
CHANNEL_ID_DICT = {
    CZCE_SR: 5,
    DCE_C: 1,
}
# 符昕 -> 符光 -> 剩下的
CAT_PRIORITY_LIST = [CZCE_SR, SHFE_RU, DCE_C, CZCE_SA, CZCE_FG, SHFE_WR]
FED = "美联储利率"
BDI = "波罗的海干散货指数"
EXCHANGE = "美元汇率"
DATA_LINK_DICT = {
    FED: "https://www.federalreserve.gov/releases/h15/",
    BDI: "https://www.steelx2.com/Indices/77/Default_files/Default_files/nav.js.%E4%B8%8B%E8%BD%BD",
    EXCHANGE: "https://fx.cmbchina.com/hq/history?nbr=%25E7%25BE%258E%25E5%2585%2583",
}
SAME_DF_STR = "两个DataFrame完全相同"
TECH = "tech"
INFO = "info"
CITY_0_WEATHER = "city-0-weather"
CITY_1_WEATHER = "city-1-weather"
CITY_2_WEATHER = "city-2-weather"
CITY_3_WEATHER = "city-3-weather"
CITY_4_WEATHER = "city-4-weather"
CITY_5_WEATHER = "city-5-weather"
CITY_6_WEATHER = "city-6-weather"
CITY_7_WEATHER = "city-7-weather"
CITY_8_WEATHER = "city-8-weather"
CITY_9_WEATHER = "city-9-weather"
CITY_10_WEATHER = "city-10-weather"
CITY_WEATHER_LIST = [
    CITY_0_WEATHER,
    CITY_1_WEATHER,
    CITY_2_WEATHER,
    CITY_3_WEATHER,
    CITY_4_WEATHER,
    CITY_5_WEATHER,
    CITY_6_WEATHER,
    CITY_7_WEATHER,
    CITY_8_WEATHER,
    CITY_9_WEATHER,
    CITY_10_WEATHER,
]
DATA_NAME_TO_TYPE = {
    CZCE_SR: TECH,
    CZCE_FG: TECH,
    CZCE_SA: TECH,
    DCE_LH: TECH,
    DCE_C: TECH,
    DCE_P: TECH,
    DCE_I: TECH,
}
INFO_DATA_NAME_LIST = [FED, BDI, EXCHANGE]
for name in INFO_DATA_NAME_LIST:
    DATA_NAME_TO_TYPE[name] = INFO
DATA_NAME_SHOW_LIST = TECH_CAT_LIST + INFO_DATA_NAME_LIST
for name in CITY_WEATHER_LIST:
    DATA_NAME_TO_TYPE[name] = INFO
SHANGHAI_VAR_DICT = {
    "cu": "铜",
    "bc": "铜(BC)",
    "al": "铝",
    "zn": "锌",
    "pb": "铅",
    "ni": "镍",
    "sn": "锡",
    "ao": "氧化铝",
    "au": "黄金",
    "ag": "白银",
    "rb": "螺纹钢",
    "wr": "线材",
    "hc": "热轧卷板",
    "ss": "不锈钢",
    "sc": "原油",
    "lu": "低硫燃料油",
    "fu": "燃料油",
    "bu": "石油沥青",
    "br": "丁二烯橡胶",
    "ru": "天然橡胶",
    "nr": "20号胶",
    "sp": "纸浆",
    "ec": "SCFIS欧线",
}
SHANGHAI_PRODUCT_ID_NAME_DICT = {
    "cu_f": "铜",
    "bc_f": "铜(BC)",
    "al_f": "铝",
    "zn_f": "锌",
    "pb_f": "铅",
    "ni_f": "镍",
    "sn_f": "锡",
    "ao_f": "氧化铝",
    "au_f": "黄金",
    "ag_f": "白银",
    "rb_f": "螺纹钢",
    "wr_f": "线材",
    "hc_f": "热轧卷板",
    "ss_f": "不锈钢",
    "sc_f": "原油",
    "lu_f": "低硫燃料油",
    "fu_f": "燃料油",
    "bu_f": "石油沥青",
    "br_f": "丁二烯橡胶",
    "ru_f": "天然橡胶",
    "nr_f": "20号胶",
    "sp_f": "纸浆",
    "ec_f": "SCFIS欧线",
    "sc_tas": "原油TAS",
}
SHANGHAI_DATA_NAME_LIST = [f"上期所-{name}" for name in SHANGHAI_PRODUCT_ID_NAME_DICT.values()]
for name in SHANGHAI_DATA_NAME_LIST:
    DATA_NAME_TO_TYPE[name] = TECH
START_TIME = "开始时间"
END_TIME = "结束时间"
MAX_SHOW_NUM = 20

DCE_COLS = [
    "合约",
    "前结算价",
    "开盘价",
    "最高价",
    "最低价",
    "收盘价",
    "结算价",
    "涨跌1",
    "涨跌2",
    "成交量",
    "成交额",
    "持仓量",
]

CZCE_COLS = [
    "时间",
    "合约代码",
    "昨结算",
    "今开盘",
    "最高价",
    "最低价",
    "今收盘",
    "今结算",
    "涨跌1",
    "涨跌2",
    "成交量(手)",
    "持仓量",
    "增减量",
    "成交额(万元)",
    "交割结算价",
]

DCE_VAR_DICT = {
    "全部": "all",
    "豆一": "a",
    "豆二": "b",
    "豆粕": "m",
    "豆油": "y",
    DCE_P: "p",
    DCE_C: "c",
    "玉米淀粉": "cs",
    "鸡蛋": "jd",
    "粳米": "rr",
    "纤维板": "fb",
    "胶合板": "bb",
    DCE_LH: "lh",
    "聚乙烯": "l",
    "聚氯乙烯": "v",
    "聚丙烯": "pp",
    "苯乙烯": "eb",
    "焦炭": "j",
    "焦煤": "jm",
    DCE_I: "i",
    "乙二醇": "eg",
    "液化石油气": "pg",
}

BATCH_CRAWL_NUM = 7

CONVERTED_TECH_DIR = "converted_tech"
FULL_DIR = "full"
INCREMENT_DIR = "increment"
# ToDo(hm): change input to manual
MANUAL_DIR = "input"
CHECKPOINT_DIR = "checkpoint"
ITER_EXPERIMENT_DIR = "iter_experiment"
INIT_EXPERIMENT_DIR = "init_experiment"
BATCH_RUN_DIR = "batch_run"
CHANGE_DAY_EXPERIMENT_DIR = "change_day_experiment"
COMPARE_DIR = "compare"
IMPORT_DIR = "import"
CACHE_DIR = "cache"
EXPORT_DIR = "export"
VAR_DIR = "var"
IMAGE_DIR = "image"
CHECKPOINT_MERGE_DIR = "checkpoint_merge"
LOCK_DIR = "lock"
DATA_DIR_LIST = [
    FULL_DIR,
    INCREMENT_DIR,
    MANUAL_DIR,
    CHECKPOINT_DIR,
    ITER_EXPERIMENT_DIR,
    INIT_EXPERIMENT_DIR,
    COMPARE_DIR,
    IMPORT_DIR,
    CACHE_DIR,
    VAR_DIR,
    IMAGE_DIR,
    CHECKPOINT_MERGE_DIR,
    CHANGE_DAY_EXPERIMENT_DIR,
    LOCK_DIR,
]
DATA_DIR_WITH_CAT_LIST = [MANUAL_DIR]

FILELOCK_TIMEOUT = 10

GUARD_PID = "guard_pid"

MERGE_RESULT_FILE_NAME = "转换日合并结果.json"
BATCH_RUN_RESULT_FILE_NAME = "批量择时运行结果.json"
CONFIG_FILE_NAME = "运行参数.json"
ANS_DICT_FILE_NAME = "0_择时模型交互面板数据.json"
TRAIN_DATA_FILE_NAME = "1_训练样本信号.xlsx"
PREDICT_DATA_FILE_NAME = "1_预测样本信号.xlsx"
RUN_LOG_FILE_NAME = "运行日志.txt"
WEIGHT_AFTER_FILE_NAME = "改良后的权重.json"
WEIGHT_BEFORE_FILE_NAME = "改良前的权重.json"
HISTORY_WEIGHT_FILE_NAME = "历史权重.json"
INPUT_DATA_FILE_NAME = "补充的指标数据.xlsx"
REF_DURATION_FILE_NAME = "样本时间范围.csv"
CRAWL_SUMMARY_FILE_NAME = "数据爬取总结报告.json"
ACCOUNT_CONFIG_FILE_NAME = "ac.json"
CHANGE_DAY_EXP_CONFIG_NAME = "转换日实验运行参数.json"
SYSTEM_RUN_LOG_NAME = "ainvest_run.log"
GLOBAL_CONFIG_FILE_NAME = "全局配置.json"

EXECUTE_CRAWLER_NUM_KEY = "执行爬虫个数"
FAIL_CRAWLER_NUM_KEY = "失败爬虫个数"
FAIL_CRAWLER_LIST_KEY = "失败爬虫列表"
ZERO_CRAWLER_NUM_KEY = "成功但无数据的爬虫个数"
ZERO_CRAWLER_LIST_KEY = "成功但无数据的爬虫列表"
RUNTIME = "执行时间"
CRAWL_INFO_LIST_KEY = "爬虫结果列表"
APPEND_INFO_LIST_KEY = "合并结果列表"

INPUT_DATA_PREFIX = "补充的指标数据"

OPTIMIZE_LOWER_BOUND = -2000
OPTIMIZE_UPPER_BOUND = 2001
OPTIMIZE_PRECISION = 100

# NORMAL_EXPERIMENT_SUFFIX = "_normal"
COARSE_CAT_LIST = [SHFE_WR, CZCE_SA, CZCE_FG]

TOP_CODE_NUM = 4
ITER_TRAIN_LEN_DEFAULT = 60

PREDICT_DATE = "样本结束日"

# ans_dict key
EXPECTED_VAL = "真实值"
EXPECTED_OP_SEQ_KEY = "固定的指标优化顺序"
ACTUAL_OP_SEQ_KEY = "实际优化顺序"
WEIGHT_ARRAY_DISTANCE = "改良前后权重距离"
WEIGHT_ARRAY_MAX_DIFFERENCE = "改良前后权重最大差值"
PREDICT_VAL = "预测值(修正前)"
ACTUAL_SIGNAL_LIST = "实际使用指标"
TRAIN_RESULT = "训练结果"
BEFORE_OPTIMIZE = "改良前"
AFTER_OPTIMIZE = "改良后"
TRAIN_LEN = "样本天数"

WEEKDAYS = ["一", "二", "三", "四", "五", "六", "日"]

EMAIL_PASSWD = "ep"
EMAIL_SENDER = "es"
EMAIL_RECEIVER_LIST = "er"
ACCOUNT_KEY_LIST = [EMAIL_RECEIVER_LIST, EMAIL_PASSWD, EMAIL_SENDER]
SMTP_PORT = 0

EMAIL_PREFIX_INFO = "【通知】"
EMAIL_PREFIX_WARN = "【告警】"
EMAIL_PREFIX_IMPORTANT = "【重要】"
SEND_EMAIL_INTERVAL_MIN = 10


