from datetime import datetime
import json

from crawler.BaseSpider import BaseSpider
from article import Article
from db_manager import DBManager
from crawler.spider_config import time_format


class Spider_East_Money_ShangPin(BaseSpider):
    name = "东方财富_全球财经快讯_商品"
    # 实际爬取的是一个js
    # URL = "https://np-weblist.eastmoney.com/comm/web/getFastNewsList?client=web&biz=web_724&fastColumn=106&sortEnd=&pageSize=50&req_trace=1752750168383&_=1752750168384&callback=jQuery183020596046357913778_1752750168119"

    def __init__(self, source_id, db_conn):
        super().__init__(source_id, db_conn)
        self.realSort = ""

    def _get_page_url(self, page_number):
        """子类必须实现：返回第 page_number 页的 URL"""
        time_stamp = int(datetime.now().timestamp() * 1000)
        url = f"https://np-weblist.eastmoney.com/comm/web/getFastNewsList?client=web&biz=web_724&fastColumn=106&sortEnd={self.realSort}&pageSize=200&req_trace={time_stamp}&_={time_stamp + 2}&callback=jQuery183020596046357913778_{time_stamp + 5}"
        return url

    def parse(self, html):
        # 解析
        html_json = html[html.index("(") + 1:-1]

        data = json.loads(html_json)["data"]
        items = data['fastNewsList']

        if len(items) > 0:
            self.realSort = items[-1]["realSort"]

        article_list = []
        for item in items:
            try:
                extra = {
                    "stock": item['stockList'],
                    "comment_Num": item['pinglun_Num'],
                    "share": item['share'],
                    "titleColor": item['titleColor']
                }
                extra_json = json.dumps(extra, ensure_ascii=False)
                url = f"https://finance.eastmoney.com/a/{item['code']}.html"

                # 处理时间格式
                published_at = item['showTime']
                # 确保时间格式统一
                try:
                    # 尝试解析时间并转换为标准格式
                    dt = datetime.strptime(published_at, time_format)
                    published_at = dt.strftime(time_format)
                except:
                    # 如果已经是标准格式或其他格式，保持原样
                    pass

                # 根据titleColor判断类别
                cat = "商品"
                if item['titleColor'] == 1:
                    cat = "重要商品"

                cur_article = Article(
                    title=item["title"],
                    content=item["summary"],
                    published_at=published_at,
                    source=self.name,
                    url=url,
                    cat=cat,
                    extra=extra_json
                )
                article_list.append(cur_article)
            except Exception as e:
                # 如果单个item解析失败，记录错误但继续处理其他item
                print(f"解析东方财富商品item时出错: {e}")
                continue

        return article_list


if __name__ == '__main__':
    article_list = Spider_East_Money_ShangPin(1, None).run()
    db = DBManager()
    db.init_database()
    db.batch_insert_articles(article_list)
