from datetime import datetime
import json

from crawler.BaseSpider import BaseSpider
from article import Article
from db_manager import DBManager
from crawler.spider_config import time_format


class Spider_THS_724(BaseSpider):
    name = "同花顺_7*24"
    # 实际爬取的是一个js
    # URL = "https://news.10jqka.com.cn/realtimenews.html"
    # js的时效性不够高，存在延迟
    # URL = "https://stock.10jqka.com.cn/thsgd/realtimenews.js"
    # URL = "https://news.10jqka.com.cn/tapp/news/push/stock/?page=1&tag=&track=website&pagesize=400"

    def _get_page_url(self, page_number):
        "https://news.10jqka.com.cn/tapp/news/push/stock/?page=1&tag=&track=website&pagesize=400"
        url = f"https://news.10jqka.com.cn/tapp/news/push/stock/?page={page_number}&tag=&track=website&pagesize=400"
        return url

    def parse(self, html):
        # 解析
        data = json.loads(html)["data"]
        items = data["list"]
        # item_df = pd.DataFrame(items)
        # # short与digest不一致的行没有，结论 这俩是一样的
        # diff_row = item_df[item_df["digest"] != item_df["short"]]
        article_list = []
        for item in items:
            extra = {
                "tag": item["tag"],
                "tagInfo": item["tagInfo"],
                "stock": item["stock"],
                "field": item["field"],
            }
            extra_json = json.dumps(extra, ensure_ascii=False)
            published_at = datetime.fromtimestamp(int(item["ctime"])).strftime(time_format)

            cur_article = Article(
                title=item["title"],
                content=item["short"],
                published_at=published_at,
                source=self.name,
                url=item["url"],
                cat="",
                extra=extra_json
            )
            article_list.append(cur_article)
        return article_list


if __name__ == '__main__':
    article_list = Spider_THS_724(1, None).run()
    db = DBManager()
    db.init_database()
    db.batch_insert_articles(article_list)
