import sqlite3
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(filename)s - %(levelname)s - %(message)s',
)
# 使用
logger = logging.getLogger(__name__)
logger.info("程序启动")


class CrawlManager:
    def __init__(self, db_conn, spider_classes):
        self.db_conn = db_conn
        self.spider_classes = spider_classes

    def run_all(self, minutes=None):
        for SpiderCls in self.spider_classes:
            source_id = self._ensure_source(SpiderCls.name)
            spider = SpiderCls(source_id, self.db_conn)
            new_items = spider.run(minutes=minutes)
            new_count = self._persist(new_items)
            logger.info(
                "爬取%4d条新闻数据，其中新增%4d条新闻数据，最新消息时间为：%s，最老消息时间为：%s ｜ 消息源:%-40s",
                len(new_items),  # 爬取数量（4字符宽度，右对齐）
                new_count,  # 新增数量（3字符宽度，右对齐）
                new_items[0]['published_at'],  # 最新时间
                new_items[-1]['published_at'],# 最老时间
                SpiderCls.name,  # 消息源字段（左对齐，40字符宽度）
            )

    def _ensure_source(self, name):
        cur = self.db_conn.cursor()
        cur.execute("SELECT id FROM source WHERE name=?", (name,))
        row = cur.fetchone()
        if row:
            return row[0]
        cur.execute("INSERT INTO source(name) VALUES(?)", (name,))
        self.db_conn.commit()
        return cur.lastrowid

    def _persist(self, items):
        cur = self.db_conn.cursor()
        new_count = 0  # 新增计数器
        for rec in items:
            # 查重：判断 URL 是否已存在 :contentReference[oaicite:8]{index=8}
            cur.execute("SELECT 1 FROM news WHERE url=?", (rec["url"],))
            if cur.fetchone():
                continue

            # 获取字典的键作为列名，并构造 SQL 插入语句
            columns = ', '.join(rec.keys())
            placeholders = ', '.join('?' for _ in rec.values())  # 使用 ? 占位符防止 SQL 注入
            values = tuple(rec.values())
            sql = f"INSERT INTO news ({columns}) VALUES ({placeholders});"

            try:
                cur.execute(sql, values)
                new_count += 1  # 每成功插入一条就递增
                self.db_conn.commit()
            except sqlite3.IntegrityError as e:
                logger.error(f"插入失败: {e}")
        self.db_conn.commit()
        return new_count
