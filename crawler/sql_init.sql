CREATE TABLE IF NOT EXISTS source
(
    id         INTEGER PRIMARY KEY AUTOINCREMENT,
    name       TEXT NOT NULL UNIQUE, -- 源名称，如 “10jqka_today”
    base_url   TEXT NOT NULL,        -- 源首页或列表页 URL
    remark     TEXT NULL,            -- 备注或说明
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE IF NOT EXISTS news
(
    id           INTEGER PRIMARY KEY AUTOINCREMENT,
    source_id    INTEGER  NOT NULL,                           -- 关联 source.id
    published_at DATETIME NOT NULL,                           -- 新闻发表时间（ISO 格式）
    import       INTEGER  NOT NULL DEFAULT 0,                 -- 新闻重要性，0-5
    title        TEXT     NOT NULL,                           -- 新闻标题
    summary      TEXT     NULL,                               -- 新闻简报
    url          TEXT     NOT NULL UNIQUE,                    -- 新闻链接（去重）
    extra        TEXT     NULL,                               -- 新闻额外信息
    fetched_at   DATETIME          DEFAULT CURRENT_TIMESTAMP, -- 抓取时间
    FOREIGN KEY (source_id) REFERENCES source (id) ON DELETE SET DEFAULT
);

INSERT OR IGNORE INTO source (name, base_url, remark)
VALUES ("同花顺_7*24", "https://news.10jqka.com.cn/realtimenews.html",
        "实际爬虫链接:https://news.10jqka.com.cn/tapp/news/push/stock/?page=1&tag=&track=website&pagesize=400");

INSERT OR IGNORE INTO source (name, base_url, remark)
VALUES ("同花顺_财经_财经要闻", "https://news.10jqka.com.cn/today_list/index_1.shtml",
        "实际可翻页 https://news.10jqka.com.cn/today_list/index_1.shtml");

INSERT OR IGNORE INTO source (name, base_url, remark)
VALUES ("东方财富_全球财经快讯_7*24", "https://kuaixun.eastmoney.com/",
        "实际可翻页 https://np-weblist.eastmoney.com/comm/web/getFastNewsList?client=web&biz=web_724&fastColumn=102&sortEnd=&pageSize=400&req_trace=1750108260000&_=1750108260000&callback=jQuery18306003836258673598_1750108260000");

INSERT OR IGNORE INTO source (name, base_url, remark)
VALUES ("东方财富_全球财经快讯_商品", "https://kuaixun.eastmoney.com/sp.html",
        "实际可翻页 https://np-weblist.eastmoney.com/comm/web/getFastNewsList?client=web&biz=web_724&fastColumn=106&sortEnd=&pageSize=50&req_trace=1752750168383&_=1752750168384&callback=jQuery183020596046357913778_1752750168119");

INSERT OR IGNORE INTO source (name, base_url, remark)
VALUES ("新浪财经_7*24", "https://finance.sina.com.cn/7x24/?tag=102",
        "实际可翻页 https://zhibo.sina.com.cn/api/zhibo/feed?callback=jQuery111209748657571765837_1752875364370&page=1&page_size=20&zhibo_id=152&tag_id=102&dire=f&dpc=1&pagesize=20&id=4295731&type=0&_=1752875364372");
