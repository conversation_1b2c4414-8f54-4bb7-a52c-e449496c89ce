from datetime import datetime
from bs4 import BeautifulSoup
from crawler.BaseSpider import BaseSpider
from article import Article
from db_manager import DBManager
from crawler.spider_config import time_format


class Spider_THS_Finance(BaseSpider):
    name = "同花顺_财经_财经要闻"

    # URL = "https://news.10jqka.com.cn/today_list/"

    def _get_page_url(self, page_number):
        """子类必须实现：返回第 page_number 页的 URL"""
        url = f"https://news.10jqka.com.cn/today_list/index_{page_number}.shtml"
        return url

    def parse(self, html):
        # 解析 HTML
        soup = BeautifulSoup(html, "lxml")
        article_list = []

        for li in soup.select("body > div.content-1200 > div.module-l.fl > div.list-con > ul > li"):
            try:
                arc_title = li.find("span", class_="arc-title")
                a = arc_title.find("a")
                title = a.get_text(strip=True)
                url = a["href"]

                date_str = arc_title.find("span").get_text(strip=True)

                # 解析原始字符串（假设年份为当前年份）
                current_year = datetime.now().year
                parsed_date = datetime.strptime(f"{current_year} {date_str}", "%Y %m月%d日 %H:%M")
                # 格式化为目标格式
                published_at = parsed_date.strftime(time_format)

                arc_cont = li.find("a", class_="arc-cont")
                content = arc_cont.get_text(strip=True) if arc_cont else ""
                content = "" if content == "..." else content

                # 根据内容长度判断类别
                cat = "财经要闻" if len(content) > 0 else ""

                cur_article = Article(
                    title=title,
                    content=content,
                    published_at=published_at,
                    source=self.name,
                    url=url,
                    cat=cat,
                    extra=None
                )
                article_list.append(cur_article)
            except Exception as e:
                # 如果单个item解析失败，记录错误但继续处理其他item
                print(f"解析同花顺财经item时出错: {e}")
                continue

        return article_list


if __name__ == '__main__':
    article_list = Spider_THS_Finance(1, None).run()
    db = DBManager()
    db.init_database()
    db.batch_insert_articles(article_list)
