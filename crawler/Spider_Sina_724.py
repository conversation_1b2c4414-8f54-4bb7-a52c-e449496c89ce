from datetime import datetime
import json

from crawler.BaseSpider import BaseSpider
from article import Article
from db_manager import DBManager
from crawler.spider_config import time_format


class Spider_Sina_724(BaseSpider):
    name = "新浪财经_7*24"
    # 实际爬取的是一个js
    # URL = "https://zhibo.sina.com.cn/api/zhibo/feed?callback=jQuery111208526555190813588_1752875790352&page=1&page_size=50&zhibo_id=152&tag_id=0"

    def _get_page_url(self, page_number):
        timestamp = int(datetime.now().timestamp() * 1000)
        url = f"https://zhibo.sina.com.cn/api/zhibo/feed?callback=jQuery111208526555190813588_{timestamp}&page={page_number}&page_size=50&zhibo_id=152&tag_id=0"
        return url

    def parse(self, html):
        # 解析
        html_json = html[html.index("(") + 1:html.index("catch(e)") - 3]
        data = json.loads(html_json)
        items = data["result"]["data"]["feed"]["list"]

        article_list = []
        for item in items:
            try:
                extra = {
                    "tag": item["tag"],
                    "ext": item["ext"],
                }
                # 如果有多媒体内容，则保存
                if 'multimedia' in item and item['multimedia']:
                    extra['multimedia'] = item['multimedia']

                extra_json = json.dumps(extra, ensure_ascii=False)
                rich_text = item["rich_text"]

                # 解析标题和内容
                try:
                    title_index = rich_text.index("】")
                    title = rich_text[1:title_index]
                    content = rich_text[title_index + 1:]
                except:
                    title = rich_text
                    content = ""

                # 处理时间格式 - 新浪的时间格式可能需要转换
                published_at = item['create_time']
                # 如果时间格式不是标准格式，进行转换
                if not published_at.count('-') == 2 or not published_at.count(':') == 2:
                    # 尝试解析并转换为标准格式
                    try:
                        dt = datetime.strptime(published_at, "%Y-%m-%d %H:%M:%S")
                        published_at = dt.strftime(time_format)
                    except:
                        # 如果解析失败，使用当前时间
                        published_at = datetime.now().strftime(time_format)

                url = item['docurl'] if item['docurl'] != "" else f"no url at {item['create_time']}"

                # 根据标签判断类别
                cat = ""
                for tag in item["tag"]:
                    if tag["name"] == "焦点":
                        cat = "焦点"
                        break

                cur_article = Article(
                    title=title,
                    content=content,
                    published_at=published_at,
                    source=self.name,
                    url=url,
                    cat=cat,
                    extra=extra_json
                )
                article_list.append(cur_article)
            except Exception as e:
                # 如果单个item解析失败，记录错误但继续处理其他item
                print(f"解析新浪财经item时出错: {e}")
                continue

        return article_list


if __name__ == '__main__':
    article_list = Spider_Sina_724(1, None).run()
    db = DBManager()
    db.init_database()
    db.batch_insert_articles(article_list)
