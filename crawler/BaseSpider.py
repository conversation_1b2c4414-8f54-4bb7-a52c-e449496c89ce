import logging
import time
from datetime import datetime, timedelta

import requests
from crawler.spider_config import headers, time_format

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(filename)s - %(levelname)s - %(message)s',
)
# 使用
logger = logging.getLogger(__name__)
logger.info("程序启动")


class BaseSpider:
    name = ""
    URL = ""

    def __init__(self, source_id, db_conn):
        self.source_id = source_id
        self.db_conn = db_conn
        self.items = []

    def fetch(self, url):
        """发送请求，返回页面 HTML"""
        # # 从数据库中查询到base url
        # base_url = self.db_conn.execute("SELECT base_url FROM source WHERE id = ?", (self.source_id,)).fetchone()
        # headers["Referer"] = base_url[0]
        resp = requests.get(url, headers=headers)  # 通用请求逻辑
        resp.raise_for_status()
        return resp.text

    def parse(self, html):
        """
        子类必须实现：解析 HTML 并返回该页的所有 item 列表
        每个 item 应包含 'title', 'published_at'(datetime), 'summary', 'url' 等字段
        """
        raise NotImplementedError

    def _get_page_url(self, page_number):
        """子类必须实现：返回第 page_number 页的 URL"""
        raise NotImplementedError

    def run(self, minutes=None):
        """

        :param minutes: 爬取的新闻距现在的时间
        :return:
        """
        if minutes is None:
            # 如果没有写小时数，则只抓取一次
            html = self.fetch(self._get_page_url(1))
            self.items = self.parse(html)
        else:
            # 写了小时数，则持续抓取，直到最后一条新闻的时间超过 hours 小时
            # 最大翻页数量
            max_pages = 50
            now_time = datetime.now()
            end_time = now_time - timedelta(minutes=minutes)
            end_time_str = end_time.strftime(time_format)
            this_page_num = 1
            while this_page_num <= max_pages:
                this_page_url = self._get_page_url(this_page_num)
                html = self.fetch(this_page_url)
                this_page_items = self.parse(html)
                if len(this_page_items) <= 0:
                    # 如果当前页面没有数据，则结束循环
                    break
                # 追加数据到items
                self.items.extend(this_page_items)
                # 判断是否超过时间限制
                last_item_time = this_page_items[-1]["published_at"]
                logger.info(
                    f"已爬取第{this_page_num}页，爬取新闻数量为：{len(this_page_items)}，最老的消息时间为：{last_item_time}，消息源：{self.name}")
                if last_item_time < end_time_str:
                    # 如果最后一条数据时间小于开始时间，则结束循环
                    break
                this_page_num += 1
                time.sleep(1)
        return self.items
