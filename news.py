import streamlit as st
from datetime import datetime, timedelta
from typing import List, Dict, Any

from constant import CHOOSE_CAT_LIST
from db_manager import DBManager
from crawler_manager import get_crawl_status_summary, get_last_crawl_errors, crawler_manager
from article import Article


def apply_time_filter(news_data: List[Article], time_filter: str, start_date=None, end_date=None) -> List[Article]:
    """应用时间筛选"""
    if time_filter == "全部":
        return news_data

    now = datetime.now()
    filtered_news = []

    for news in news_data:
        try:
            news_time = datetime.strptime(news.published_at, '%Y-%m-%d %H:%M:%S')

            if time_filter == "今天":
                if (now - news_time).days == 0:
                    filtered_news.append(news)
            elif time_filter == "最近三天":
                if (now - news_time).days <= 3:
                    filtered_news.append(news)
            elif time_filter == "最近一周":
                if (now - news_time).days <= 7:
                    filtered_news.append(news)
            elif time_filter == "最近一个月":
                if (now - news_time).days <= 30:
                    filtered_news.append(news)
            elif time_filter == "自定义" and start_date and end_date:
                start_datetime = datetime.combine(start_date, datetime.min.time())
                end_datetime = datetime.combine(end_date, datetime.max.time())
                if start_datetime <= news_time <= end_datetime:
                    filtered_news.append(news)
        except ValueError:
            # 跳过时间格式错误的数据
            continue

    return filtered_news


def render_news_card(news: Article, index: int) -> None:
    """渲染单个新闻卡片"""
    try:
        formatted_time = datetime.strptime(news.published_at, '%Y-%m-%d %H:%M:%S').strftime('%m-%d %H:%M')
    except ValueError:
        formatted_time = news.published_at

    # 截断过长的内容
    content = news.content[:200] + "..." if len(news.content) > 200 else news.content

    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 12px;
        border-left: 4px solid #1f77b4;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    ">
        <div style="
            color: #1f77b4;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.4;
        ">{news.title}</div>
        <div style="
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
            font-size: 12px;
            color: #6c757d;
        ">
            <span style="
                background: #1f77b4;
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 11px;
            ">{news.cat}</span>
            <span>{formatted_time}</span>
            <a href="{news.url}" target="_blank" style="
                color: #1f77b4;
                text-decoration: none;
                font-weight: 500;
            ">{news.source}</a>
        </div>
        <div style="
            color: #495057;
            font-size: 14px;
            line-height: 1.5;
        ">{content}</div>
    </div>
    """, unsafe_allow_html=True)


def show_compact_status(filtered_news: List[Article]) -> None:
    """显示紧凑的状态信息"""
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("📰 新闻总数", len(filtered_news))

    with col2:
        if filtered_news:
            try:
                latest_time = datetime.strptime(filtered_news[0].published_at, '%Y-%m-%d %H:%M:%S')
                st.metric("🕒 最新更新", latest_time.strftime("%H:%M"))
            except ValueError:
                st.metric("🕒 最新更新", "无")
        else:
            st.metric("🕒 最新更新", "无")

    with col3:
        crawl_status = get_crawl_status_summary()
        if crawl_status["has_errors"]:
            st.metric("⚠️ 数据源", f"{crawl_status['failed_sources']} 个异常", delta_color="inverse")
        else:
            st.metric("✅ 数据源", "全部正常")


def show_data_update_section() -> None:
    """显示数据更新区域"""
    with st.expander("📡 数据更新", expanded=False):
        available_sources = crawler_manager.get_available_sources()

        # 显示数据源状态
        st.write("**数据源状态:**")
        cols = st.columns(2)
        for i, source in enumerate(available_sources):
            with cols[i % 2]:
                latest_time = crawler_manager.get_latest_time_for_source(source)
                if latest_time:
                    time_str = latest_time.split()[1] if ' ' in latest_time else latest_time[-8:]
                    st.write(f"• {source}: {time_str}")
                else:
                    st.write(f"• {source}: 暂无数据")

        st.divider()

        # 更新控制
        col1, col2 = st.columns(2)
        with col1:
            selected_source = st.selectbox("选择数据源", available_sources, key="update_source")
            if st.button("🔄 更新选中", key="update_single", use_container_width=True):
                with st.spinner(f"更新 {selected_source}..."):
                    result = crawler_manager.crawl_incremental_data(selected_source)
                    if result["success"]:
                        st.success(f"✅ {result['message']}")
                        if result["inserted_count"] > 0:
                            st.balloons()
                    else:
                        st.error(f"❌ {result['message']}")

        with col2:
            st.write("")  # 占位
            st.write("")  # 占位
            if st.button("🚀 更新全部", key="update_all", use_container_width=True):
                with st.spinner("更新所有数据源..."):
                    results = crawler_manager.crawl_all_sources()
                    success_count = sum(1 for r in results.values() if r.get("success", False))
                    total_count = len(results)

                    if success_count == total_count:
                        st.success(f"✅ 全部更新完成 ({success_count}/{total_count})")
                    else:
                        st.warning(f"⚠️ 部分更新完成 ({success_count}/{total_count})")


def show_news():
    st.markdown("## 📰 新闻汇总")

    # 初始化数据库
    db = DBManager()
    db.init_database()

    # 侧边栏筛选
    with st.sidebar:
        st.header("🔍 筛选选项")

        # 时间筛选
        st.subheader("📅 时间范围")
        time_mode = st.radio(
            "筛选模式",
            ["预设范围", "自定义日期"],
            horizontal=True
        )

        if time_mode == "预设范围":
            time_filter = st.selectbox(
                "选择范围",
                ["全部", "今天", "最近三天", "最近一周", "最近一个月"]
            )
            start_date = None
            end_date = None
        else:
            # 自定义日期选择
            end_date_default = datetime.now().date()
            start_date_default = end_date_default - timedelta(days=7)

            start_date = st.date_input(
                "开始日期",
                start_date_default,
                format="YYYY-MM-DD"
            )
            end_date = st.date_input(
                "结束日期",
                end_date_default,
                format="YYYY-MM-DD"
            )

            # 验证日期范围
            if start_date > end_date:
                st.error("开始日期不能晚于结束日期")
                time_filter = "全部"  # 回退到全部
            else:
                time_filter = "自定义"

        # 排序方式
        st.subheader("📊 排序")
        sort_by = st.radio("排序方式", ["时间降序", "时间升序"], horizontal=True)

    # 加载和筛选数据
    news_data = db.load_article_list()
    filtered_news = apply_time_filter(news_data, time_filter, start_date, end_date)

    # 排序
    try:
        filtered_news.sort(
            key=lambda x: datetime.strptime(x.published_at, '%Y-%m-%d %H:%M:%S'),
            reverse=(sort_by == "时间降序")
        )
    except ValueError:
        # 如果时间格式有问题，按原始字符串排序
        filtered_news.sort(key=lambda x: x.published_at, reverse=(sort_by == "时间降序"))

    # 显示紧凑的状态信息
    show_compact_status(filtered_news)

    # 显示错误信息（如果有）
    crawl_errors = get_last_crawl_errors()
    if crawl_errors:
        st.warning(f"⚠️ {len(crawl_errors)} 个数据源异常，可能影响数据完整性")
        with st.expander("查看错误详情", expanded=False):
            for source_name, error_info in crawl_errors.items():
                st.error(f"**{source_name}**: {error_info.get('message', '未知错误')}")
                if error_info.get("timestamp"):
                    st.caption(f"时间: {error_info['timestamp']}")

    # 数据更新区域
    show_data_update_section()

    st.divider()
    # 新闻列表显示
    if not filtered_news:
        st.info("📭 没有找到符合条件的新闻")
        return

    st.subheader(f"📋 新闻列表 ({len(filtered_news)} 条)")

    # 分页显示
    items_per_page = 20
    total_pages = (len(filtered_news) - 1) // items_per_page + 1

    if total_pages > 1:
        page = st.selectbox(
            "选择页面",
            range(1, total_pages + 1),
            format_func=lambda x: f"第 {x} 页 (共 {total_pages} 页)"
        )
        start_idx = (page - 1) * items_per_page
        end_idx = min(start_idx + items_per_page, len(filtered_news))
        page_news = filtered_news[start_idx:end_idx]
    else:
        page_news = filtered_news

    # 渲染新闻卡片
    for idx, news in enumerate(page_news):
        render_news_card(news, idx)
