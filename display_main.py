import argparse
import streamlit as st

from news import show_news

CHOOSE_TIME_BATCH = "批量择时"
ITER_EXPERIMENT = "迭代实验"
INIT_EXPERIMENT = "初始实验"
CHANGE_DAY_EXPERIMENT = "转换日实验"
CHANGE_DAY_MERGE = "转换日融合"
OPS = "运维"
DATA_DASHBOARD = "数据"
BACKTEST = "回测"
NEWS = "新闻"
OP_SEQ = [
    NEWS,
    CHOOSE_TIME_BATCH,
    ITER_EXPERIMENT,
    INIT_EXPERIMENT,
    CHANGE_DAY_MERGE,
    CHANGE_DAY_EXPERIMENT,
    BACKTEST,
    DATA_DASHBOARD,
    OPS
]

ICON_DICT = {
    "线上": ":apple:",
    "线上-副本": ":cherries:",
    "预发": ":green_apple:",
    "线下": ":four_leaf_clover:",
    "本地环境": ":mushroom:",
}


def main():
    parser = argparse.ArgumentParser()
    args = parser.parse_args()
    st.set_page_config(
        layout="wide",
        initial_sidebar_state="collapsed",
        page_title="读新闻",
        page_icon=ICON_DICT["线上"],
    )
    show_news()


if __name__ == "__main__":
    main()
