#!/usr/bin/env python3
"""
测试错误处理功能
"""

from crawler_manager import crawler_manager, _crawl_status_cache, get_crawl_status_summary, get_last_crawl_errors
from datetime import datetime

def test_error_handling():
    """测试错误处理功能"""
    print("=== 测试爬虫错误处理功能 ===\n")
    
    # 1. 测试不支持的数据源
    print("1. 测试不支持的数据源:")
    result = crawler_manager.crawl_incremental_data("不存在的数据源")
    print(f"   结果: {result['success']}")
    print(f"   消息: {result['message']}")
    print(f"   错误类型: {result.get('error_type')}")
    print(f"   错误详情: {result.get('error_details')}")
    print()
    
    # 2. 模拟网络错误
    print("2. 模拟一个成功的爬取（如果可能）:")
    available_sources = crawler_manager.get_available_sources()
    if available_sources:
        source_name = available_sources[0]
        print(f"   尝试爬取: {source_name}")
        try:
            result = crawler_manager.crawl_incremental_data(source_name)
            print(f"   结果: {result['success']}")
            print(f"   消息: {result['message']}")
            if result.get('error_type'):
                print(f"   错误类型: {result['error_type']}")
                print(f"   错误详情: {result.get('error_details')}")
        except Exception as e:
            print(f"   发生异常: {e}")
    print()
    
    # 3. 测试状态摘要
    print("3. 测试状态摘要:")
    status = get_crawl_status_summary()
    print(f"   总数据源: {status['total_sources']}")
    print(f"   成功数量: {status['successful_sources']}")
    print(f"   失败数量: {status['failed_sources']}")
    print(f"   有错误: {status['has_errors']}")
    print(f"   最后更新: {status['last_update']}")
    print(f"   错误源: {status['error_sources']}")
    print()
    
    # 4. 测试错误详情
    print("4. 测试错误详情:")
    errors = get_last_crawl_errors()
    if errors:
        for source, error_info in errors.items():
            print(f"   {source}:")
            print(f"     消息: {error_info.get('message')}")
            print(f"     类型: {error_info.get('error_type')}")
            print(f"     时间: {error_info.get('timestamp')}")
    else:
        print("   没有错误记录")
    print()
    
    # 5. 显示缓存状态
    print("5. 当前缓存状态:")
    print(f"   缓存条目数: {len(_crawl_status_cache)}")
    for source, result in _crawl_status_cache.items():
        print(f"   {source}: {'成功' if result.get('success') else '失败'}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_error_handling()
